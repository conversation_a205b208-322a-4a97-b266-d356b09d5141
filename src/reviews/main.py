from src.reviews.pipelines import sales_pipeline, employee_pipeline, products_pipeline
from src.core.constants import REPVUE_PROCESSING_WINDOW, BLIND_PROCESSING_WINDOW, TRUSTPILOT_PROCESSING_WINDOW
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory

connection = DatabaseFactory().get_mongo_connection()
sales_ticker_collection = connection.get_blackberry_sales_collection("ticker_identifier")
employee_ticker_collection = connection.get_blackberry_employee_collection("blind_companies")
products_ticker_collection = connection.get_blackberry_product_collection("trustpilot_companies")

def process_reviews():
    CUTOFF_DATE = datetime.today() - timedelta(days=REPVUE_PROCESSING_WINDOW)
    END_DATE = datetime.today()
    for doc in sales_ticker_collection.find():
        ticker = doc["ticker"]
        slug = doc["slug"]
        sales_pipeline.process_one_ticker(ticker, slug, CUTOFF_DATE, END_DATE)
    
    # for doc in employee_ticker_collection.find():
    #     ticker = doc["ticker"]
    #     employee_pipeline.process_one_ticker(ticker, CUTOFF_DATE, END_DATE)