from src.reviews.core.preprocessing import preprocess_reviews
from src.reviews.core.categorization import categorize_reviews
from src.reviews.storage.chunks import products_store_review_chunks
from src.reviews.storage.insights import employee_update_insights_collection
from src.reviews.storage.summary import create_employee_review_summary
from src.core.logging import get_logger, configure_logging
from datetime import datetime, timedelta
from pymongo.collection import Collection
from src.database.factory import DatabaseFactory
from src.core.constants import BLIND_PROCESSING_WINDOW

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()

ticker_collection: Collection = connection.get_blackberry_employee_collection("trustpilot_companies")
reviews_collection: Collection = connection.get_blackberry_product_collection("trustpilot_reviews")

insights_collection: Collection = connection.get_product_insight_collection("tp_insights")
review_chunks_collection: Collection = connection.get_product_insight_collection("review_chunks")
summary_collection: Collection = connection.get_product_insight_collection("tp_summary")
prompts_collection: Collection = connection.get_product_insight_collection("prompts")
categories_collection: Collection = connection.get_product_insight_collection("categories")

def process_one_ticker(ticker: str, CUTOFF_DATE: datetime, END_DATE: datetime) -> None:
    logger.info(f"Processing ticker: {ticker}")
    query = {
        "ticker": ticker,
        "cutoff_date": {
            "$gte": CUTOFF_DATE, 
            "$lt": CUTOFF_DATE + timedelta(days=1)
        }
    }
    if insights_collection.find_one(query):
        logger.info(f"Ticker {ticker} already exists in insights collection. Skipping...")
        return
    logger.info(f"Processing ticker {ticker}")

    query = {
        "date": {
            "$gt": CUTOFF_DATE.strftime("%Y-%m-%d"), 
            "$lt": END_DATE.strftime("%Y-%m-%d")
        },
        "ticker": ticker
    }
    review_articles_string = preprocess_reviews(query, reviews_collection, field_name="content", id_prefix="")

    prompt_doc = prompts_collection.find_one({"prompt_name": "products_reviews_categorization_prompt"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return
    
    categories_doc = categories_collection.find_one({"name": "products_categories"})
    if not categories_doc:
        logger.error("Categories not found")
        return
    
    review_list = categorize_reviews(prompt_doc["prompt"], ticker=ticker, reviews_articles_str=review_articles_string)

    for review in review_list:
        products_store_review_chunks(ticker, review, review_chunks_collection, CUTOFF_DATE, END_DATE)