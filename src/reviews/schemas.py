from pydantic import BaseModel, field_validator


class ReviewCategory(BaseModel):
    review_id: str
    review_text: str

    # @field_validator("review_id", mode="before")
    # def coerce_review_id(cls, v):
    #     return str(v)

class SalesReviewCategory(ReviewCategory):
    category: str

class EmployeeReviewCategory(ReviewCategory):
    category: str

class ProductReviewCategory(ReviewCategory):
    product: str