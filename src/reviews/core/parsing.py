from typing import List
import re
import json
from pydantic import TypeAdapter, ValidationError
from src.reviews.schemas import ReviewCategory
from src.core.logging import get_logger, configure_logging

logger = get_logger(__name__)
configure_logging()

def parse_categorization_results(ai_response: str, OutputSchema: ReviewCategory) -> List[ReviewCategory]:
    """
    Extracts and validates JSON from AI response with error handling
    """
    match = re.search(r'(\{\s*\"review_id.*\}|\[.*\])', ai_response, re.DOTALL)      
    if not match:
        logger.error(f"No valid JSON found in OpenAI response - Skipping.")
        return []
    json_str = match.group(1).strip()
    try:
        review_list = json.loads(match.group(1)) 
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON categories : {e}")
        return []

    if isinstance(review_list, dict):
        review_list = [review_list] 

    adapter = TypeAdapter(List[OutputSchema])
    try:
        validated_reviews = adapter.validate_python(review_list)
    except ValidationError as e:
        logger.error(f"Validation error: {e}")
        return []
    except Exception as e:
        logger.error(f"Unknown error in JSON validation: {e}")
        return []

    return validated_reviews