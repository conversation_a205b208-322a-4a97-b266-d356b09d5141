from src.reviews.core.preprocessing import preprocess_reviews
from src.reviews.core.categorization import categorize_reviews
from src.reviews.storage.chunks import sales_store_review_chunks
from src.reviews.storage.insights import sales_update_insights_collection
from src.reviews.storage.aggregation import sales_aggregate_insights
from src.reviews.storage.summary import create_sales_review_summary
from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.constants import REPVUE_PROCESSING_WINDOW

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()

reviews_collection: Collection = connection.get_blackberry_sales_collection("reviews")

insights_collection: Collection = connection.get_sales_collection("sales_insights")
review_chunks_collection: Collection = connection.get_sales_collection("review_chunks")
summary_collection: Collection = connection.get_sales_collection("sales_summary")
prompts_collection: Collection = connection.get_sales_collection("prompts")
categories_collection: Collection = connection.get_sales_collection("categories")

def process_one_ticker(ticker: str, slug: str, CUTOFF_DATE: datetime, END_DATE: datetime) -> None:
    ticker_search_insight_query = {
        "ticker": ticker,
        "cutoff_date": {
            "$gte": CUTOFF_DATE,
            "$lt": CUTOFF_DATE + timedelta(days=1)
        }
    }
    if insights_collection.find_one(ticker_search_insight_query):
        logger.info(f"Ticker {ticker} already exists in insights collection. Skipping...")
        return
    logger.info(f"Processing ticker {ticker}")
    
    reviews_collection_query = {
        "created_at": {
            "$gt": CUTOFF_DATE.strftime("%Y-%m-%d"), 
            "$lt": END_DATE.strftime("%Y-%m-%d")
        },
        "slug": slug
    }
    reviews_articles_string = preprocess_reviews(reviews_collection_query, reviews_collection, field_name="text")

    prompt_doc = prompts_collection.find_one({"prompt_name": "sales_reviews_categorization_prompt"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return
    
    categories_doc = categories_collection.find_one({"name": "sales_categories"})
    if not categories_doc:
        logger.error("Categories not found")
        return
    
    review_list = categorize_reviews(prompt_doc["prompt"], reviews_articles_str=reviews_articles_string, slug=slug, categories=categories_doc["categories"])
    for review in review_list:
        sales_store_review_chunks(ticker, slug, review, reviews_collection, review_chunks_collection, CUTOFF_DATE, END_DATE)

    categories_for_ticker = review_chunks_collection.distinct("category", {"ticker": ticker})
    for category in categories_for_ticker:
        review_count = review_chunks_collection.count_documents({"ticker": ticker, "category": category})
        if review_count == 0:
            logger.info(f"No reviews found for category {category}. Skipping...")
            continue
        logger.info(f"Number of reviews for category '{category}': {review_count}")
        sales_update_insights_collection(ticker, slug, category, review_count, review_chunks_collection, insights_collection, CUTOFF_DATE, END_DATE)
    
    sales_aggregate_insights(ticker, slug, insights_collection, CUTOFF_DATE)
    create_sales_review_summary(CUTOFF_DATE, END_DATE, insights_collection, summary_collection)