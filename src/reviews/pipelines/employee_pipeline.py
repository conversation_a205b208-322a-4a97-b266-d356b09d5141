from src.reviews.core.preprocessing import preprocess_reviews
from src.reviews.core.categorization import categorize_reviews
from src.reviews.storage.chunks import employee_store_review_chunks
from src.reviews.storage.insights import employee_update_insights_collection
from src.reviews.storage.summary import create_employee_review_summary
from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()

ticker_collection: Collection = connection.get_blackberry_employee_collection("blind_companies")
articles_collection: Collection = connection.get_blackberry_employee_collection("articles")
comments_collection: Collection = connection.get_blackberry_employee_collection("comments")

insights_collection: Collection = connection.get_employee_insight_collection("blind_insights")
review_chunks_collection: Collection = connection.get_employee_insight_collection("review_chunks")
summary_collection: Collection = connection.get_employee_insight_collection("blind_summary")
prompts_collection: Collection = connection.get_employee_insight_collection("prompts")
categories_collection: Collection = connection.get_employee_insight_collection("categories")

def process_one_ticker(ticker: str, CUTOFF_DATE: datetime, END_DATE: datetime) -> None:
    logger.info(f"Processing ticker: {ticker}")
    query = {
        "company_ticker": ticker,
        "date": {
            "$gt": CUTOFF_DATE, 
            "$lt": END_DATE
        },
        "$expr": {
            "$eq": ["$author_company", "$company_slug"]
        }
    }
    review_article_string1 = preprocess_reviews(query, comments_collection, field_name="content", id_prefix="a_")
    review_article_string2 = preprocess_reviews(query, articles_collection, field_name="description", id_prefix="b_")
    reviews_articles_string = review_article_string1 + " " + review_article_string2

    prompt_doc = prompts_collection.find_one({"prompt_name": "employee_reviews_categorization_prompt"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return
    
    categories_doc = categories_collection.find_one({"name": "employee_categories"})
    if not categories_doc:
        logger.error("Categories not found")
        return

    review_list = categorize_reviews(prompt_doc["prompt"], ticker=ticker, categories=categories_doc["categories"], reviews_articles_str=reviews_articles_string)

    for review in review_list:
        employee_store_review_chunks(ticker, comments_collection, articles_collection, review_chunks_collection, review, CUTOFF_DATE, END_DATE)

    categories_for_ticker = review_chunks_collection.distinct("category", {"ticker": ticker})
    for category in categories_for_ticker:
        review_count = review_chunks_collection.count_documents({"ticker": ticker, "category": category})
        reviews = review_chunks_collection.find({"ticker": ticker, "category": category, "cutoff_date": CUTOFF_DATE})
        print(f"Number of reviews for category '{category}': {review_count}")
        review_text = " ".join([f"[review_id: {review['review_id_modified']}] {review['review_text']}" for review in reviews])
        if not review_text:
            print(f"No review text found for category: {category}")
            continue
        employee_update_insights_collection(ticker, category, review_count, review_text, insights_collection, CUTOFF_DATE, END_DATE)

    create_employee_review_summary(CUTOFF_DATE, END_DATE, insights_collection, summary_collection)